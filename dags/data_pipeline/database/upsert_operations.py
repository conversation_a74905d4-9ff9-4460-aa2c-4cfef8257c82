import asyncio
import functools
import json
import sys
import time
import traceback
from datetime import datetime, date, timezone
from decimal import Decimal
from itertools import islice
from logging import Logger
from typing import Type, Any, Dict, List, Union

import numpy as np
import pandas as pd

from sqlalchemy.exc import InterfaceError, DisconnectionError, IntegrityError, PendingRollbackError
from asyncpg import DeadlockDetectedError
from psycopg2.errors import DeadlockDetected
from sqlalchemy import inspect, and_, UniqueConstraint, text
from sqlalchemy.dialects.postgresql import insert, dialect
from sqlalchemy.exc import InterfaceError, DisconnectionError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session
from sqlalchemy.sql.elements import BinaryExpression, BooleanClauseList

from dags.data_pipeline.dataframe_utils.dataframe_debugger import quick_save_async
from dags.data_pipeline.dbmodels.base import Base



async def get_pg_stat_activity(session: AsyncSession) -> list[dict]:
    query = text("""
        SELECT pid, usename, query, state, wait_event_type, wait_event, now() - query_start AS duration
        FROM pg_stat_activity
        WHERE state != 'idle'
        ORDER BY duration DESC
        LIMIT 10;
    """)
    result = await session.execute(query)
    return [dict(row._mapping) for row in result.fetchall()]


async def get_blocking_info(session: AsyncSession) -> list[dict]:
    query = text("""
    SELECT
      blocked_locks.pid AS blocked_pid,
      blocked_activity.query AS blocked_query,
      blocking_locks.pid AS blocking_pid,
      blocking_activity.query AS blocking_query
    FROM pg_locks blocked_locks
    JOIN pg_stat_activity blocked_activity
      ON blocked_activity.pid = blocked_locks.pid
    JOIN pg_locks blocking_locks
      ON blocking_locks.locktype = blocked_locks.locktype
     AND blocking_locks.database IS NOT DISTINCT FROM blocked_locks.database
     AND blocking_locks.relation IS NOT DISTINCT FROM blocked_locks.relation
     AND blocking_locks.page IS NOT DISTINCT FROM blocked_locks.page
     AND blocking_locks.tuple IS NOT DISTINCT FROM blocked_locks.tuple
     AND blocking_locks.virtualxid IS NOT DISTINCT FROM blocked_locks.virtualxid
     AND blocking_locks.transactionid IS NOT DISTINCT FROM blocked_locks.transactionid
     AND blocking_locks.classid IS NOT DISTINCT FROM blocked_locks.classid
     AND blocking_locks.objid IS NOT DISTINCT FROM blocked_locks.objid
     AND blocking_locks.objsubid IS NOT DISTINCT FROM blocked_locks.objsubid
     AND blocking_locks.pid != blocked_locks.pid
    JOIN pg_stat_activity blocking_activity
      ON blocking_activity.pid = blocking_locks.pid;
    """)
    result = await session.execute(query)
    return [dict(row._mapping) for row in result.fetchall()]


def _format_array_for_postgres(array_value):
    """Convert Python list to PostgreSQL array format."""
    if not array_value:
        return "ARRAY[]"

    # Handle different types of array elements
    formatted_elements = []
    for item in array_value:
        if item is None:
            formatted_elements.append("NULL")
        elif isinstance(item, bool):  # Check bool before int since bool is subclass of int
            formatted_elements.append("true" if item else "false")
        elif isinstance(item, str):
            # Escape single quotes and wrap in quotes
            escaped = item.replace("'", "''")
            formatted_elements.append(f"'{escaped}'")
        elif isinstance(item, (int, float)):
            formatted_elements.append(str(item))
        elif isinstance(item, (date, datetime)):
            formatted_elements.append(f"'{item.isoformat()}'")
        elif isinstance(item, dict):
            # JSON objects in arrays
            formatted_elements.append(f"'{json.dumps(item)}'")
        else:
            # Fallback - convert to string
            formatted_elements.append(f"'{str(item)}'")

    return f"ARRAY[{', '.join(formatted_elements)}]"

def _is_json_string(value):
    """Check if a string is valid JSON."""
    if not isinstance(value, str):
        return False
    try:
        json.loads(value)
        return True
    except (json.JSONDecodeError, TypeError):
        return False

def _format_value_for_sql(value: Any) -> str:
    """
    Format a Python value as a SQL literal string for PostgreSQL.

    Args:
        value: The value to format

    Returns:
        SQL literal string representation of the value
    """
    if value is None:
        return "NULL"
    elif isinstance(value, bool):
        return "true" if value else "false"
    elif isinstance(value, (int, float)):
        return str(value)
    elif isinstance(value, Decimal):
        return str(value)
    elif isinstance(value, str):
        # Escape single quotes and wrap in quotes
        escaped = value.replace("'", "''")
        return f"'{escaped}'"
    elif isinstance(value, datetime):
        if value.tzinfo is not None:
            # Convert to UTC and format as timestamptz
            value_utc = value.astimezone(timezone.utc)
            return f"'{value_utc.isoformat()}'::timestamptz"
        else:
            # Naive datetime - format as timestamp
            return f"'{value.isoformat()}'::timestamp"
    elif isinstance(value, date):
        return f"'{value.isoformat()}'::date"
    elif isinstance(value, pd.Timestamp):
        # Convert pandas Timestamp to Python datetime
        dt = value.to_pydatetime()
        return _format_value_for_sql(dt)
    elif isinstance(value, dict):
        # JSON/JSONB data
        json_str = json.dumps(value).replace("'", "''")
        return f"'{json_str}'::jsonb"
    elif isinstance(value, list):
        # Array data
        return _format_array_for_postgres(value)
    elif pd.isna(value):
        # Handle pandas NaT/NaN values
        return "NULL"
    elif hasattr(value, 'to_pydatetime') and callable(getattr(value, 'to_pydatetime')):
        # Convert pandas datetime-like objects to Python datetime
        try:
            dt = value.to_pydatetime()
            return _format_value_for_sql(dt)
        except Exception:
            # Fallback to string representation
            return f"'{str(value).replace(chr(39), chr(39)+chr(39))}'"
    else:
        # Fallback - convert to string and escape
        return f"'{str(value).replace(chr(39), chr(39)+chr(39))}'"


def compile_upsert_with_values(stmt, engine, sample_batch: List[Dict[str, Any]], max_rows: int = 5) -> str:
    """
    Compiles the upsert statement with actual values for EXPLAIN debugging.

    Args:
        stmt: SQLAlchemy insert statement
        engine: Database engine
        sample_batch: List of dictionaries containing row data
        max_rows: Maximum number of rows to include in the compiled statement

    Returns:
        SQL statement string with literal values, or error message
    """
    try:
        if not sample_batch:
            return "No data in sample_batch"

        # Limit the number of rows to avoid overly long SQL
        rows_to_process = sample_batch[:max_rows]

        # Process each row to handle different data types
        processed_rows = []
        for row_idx, row in enumerate(rows_to_process):
            processed_row = {}
            for key, value in row.items():
                try:
                    # Use our robust value formatter
                    processed_row[key] = _format_value_for_sql(value)
                except Exception as format_err:
                    # If formatting fails, use a safe fallback
                    processed_row[key] = f"'<FORMAT_ERROR: {str(format_err)}'"
            processed_rows.append(processed_row)

        # Build the VALUES clause manually since literal_binds has issues with complex types
        table_name = stmt.table.name
        if hasattr(stmt.table, 'schema') and stmt.table.schema:
            table_name = f"{stmt.table.schema}.{table_name}"

        # Get column names from the first row
        if not processed_rows:
            return "No processed rows available"

        columns = list(processed_rows[0].keys())
        column_list = ", ".join(columns)

        # Build VALUES clauses
        values_clauses = []
        for row in processed_rows:
            values = [row.get(col, "NULL") for col in columns]
            values_clauses.append(f"({', '.join(values)})")

        values_clause = ",\n    ".join(values_clauses)

        # Get the base INSERT statement structure
        base_sql = str(stmt.compile(dialect=dialect()))

        # Extract the conflict handling part if it exists
        conflict_part = ""
        if "ON CONFLICT" in base_sql:
            conflict_start = base_sql.find("ON CONFLICT")
            conflict_part = base_sql[conflict_start:]

        # Construct the final SQL
        final_sql = f"""INSERT INTO {table_name} ({column_list})
        VALUES
        {values_clause}
        """

        if conflict_part:
            final_sql += f"\n{conflict_part}"

        return final_sql

    except Exception as e:
        # Provide detailed error information for debugging
        error_details = [f"Main error: {str(e)}", f"Error type: {type(e).__name__}"]

        if sample_batch:
            error_details.append(f"Sample batch size: {len(sample_batch)}")
            if sample_batch[0]:
                error_details.append(f"First row keys: {list(sample_batch[0].keys())}")
                # Show problematic values
                for key, value in list(sample_batch[0].items())[:5]:  # First 5 items
                    error_details.append(f"  {key}: {type(value).__name__} = {repr(value)[:100]}")

        return f"Failed to compile statement:\n" + "\n".join(error_details)


def smart_retry(
        min_wait=5, max_wait=10, max_delay=60, max_retries=5,
        my_logger: Logger | None=None
):
    # Validate input arguments
    if min_wait <= 0 or max_wait <= 0 or max_delay <= 0:
        raise ValueError("Wait times and max_delay must be positive numbers.")
    if min_wait > max_wait:
        raise ValueError("min_wait cannot be greater than max_wait.")
    if max_retries <= 0:
        raise ValueError("max_retries must be a positive integer.")
    if max_delay < min_wait:
        raise ValueError("max_delay must be greater than or equal to min_wait.")

    def decorator(func_local):
        @functools.wraps(func_local)
        def wrapper(*args, **kwargs):
            start_time = time.time()

            async def async_retry_logic():
                retries = 0
                while retries < max_retries:
                    try:
                        return await func_local(*args, **kwargs)  # Await for async functions
                    except DeadlockDetectedError as e:
                        retries += 1
                        elapsed_time = (time.time() - start_time)
                        if elapsed_time >= max_delay:
                            raise TimeoutError(
                                f"Retry limit of {max_delay} seconds reached after {retries} attempts."
                            ) from e
                        wait_time = min(max_wait, min_wait * (2 ** retries))
                        if isinstance(my_logger, Logger):
                            my_logger.warning(
                                f"Async retry {retries}/{max_retries} in {wait_time:.2f} seconds due to: {e}"
                            )
                        await asyncio.sleep(wait_time)
                raise TimeoutError("Max retries reached without success.")

            def sync_retry_logic():
                retries = 0
                while retries < max_retries:
                    try:
                        return func_local(*args, **kwargs)  # Direct call for sync functions
                    except DeadlockDetected as e:
                        retries += 1
                        elapsed_time = (time.time() - start_time)
                        if elapsed_time >= max_delay:
                            raise TimeoutError(
                                f"Retry limit of {max_delay} seconds reached after {retries} attempts."
                            ) from e
                        wait_time = min(max_wait, min_wait * (2 ** retries))
                        if isinstance(my_logger, Logger):
                            my_logger.warning(
                                f"Sync retry {retries}/{max_retries} in {wait_time:.2f} seconds due to: {e}"
                            )
                        time.sleep(wait_time)
                raise TimeoutError("Max retries reached without success.")

            # Determine whether the function is async or sync
            if asyncio.iscoroutinefunction(func_local):
                return async_retry_logic()
            else:
                return sync_retry_logic()

        return wrapper

    return decorator


@smart_retry(min_wait=2, max_wait=5, max_retries=3)
def upsert(
        session: Session,
        model: Type[Base],
        rows: pd.DataFrame,
        no_update_cols: tuple[str, ...] = (),
        on_conflict_update: bool = True,
        conflict_condition: list | BinaryExpression | BooleanClauseList | None = None,
        batch_size: int | None = None,
        my_logger: Logger|None = None,
) -> None:
    """
    Perform an upsert (insert or update on conflict) operation on the given SQLAlchemy model.

    Parameters:
        session: SQLAlchemy session.
        model: SQLAlchemy model class.
        rows: DataFrame containing rows to upsert.
        no_update_cols: Columns that should not be updated on conflict.
        on_conflict_update: Whether to perform updates on conflict.
        conflict_condition: Additional conditions for conflict resolution.
        batch_size: Maximum number of rows per batch.
        my_logger: Logger instance for error logging.

    Returns:
        None
    """
    # Source: https://stackoverflow.com/questions/7165998/how-to-do-an-upsert-with-sqlalchemy/44395983#44395983
    # https://gist.github.com/bhtucker/c40578a2fb3ca50b324e42ef9dce58e1
    if rows.shape[0] == 0:
        return

    rows = rows.copy()
    rows.replace({np.nan: None}, inplace=True)
    table = model.__table__
    stmt = insert(table)
    primary_keys = [key.name for key in inspect(table).primary_key]
    update_cols = [
        c.name for c in table.c
        if c not in list(table.primary_key.columns) and c.name not in no_update_cols
    ]
    update_dict = {k: getattr(stmt.excluded, k) for k in update_cols}
    # index_where = and_(*conflict_condition) if conflict_condition else None
    if conflict_condition is not None:
        if isinstance(conflict_condition, list):
            index_where = and_(
                *(getattr(model, col) < getattr(stmt.excluded, col) for col in conflict_condition)
            )
        else:
            index_where = conflict_condition
    else:
        index_where = None

    if on_conflict_update:
        stmt = stmt.on_conflict_do_update(
            index_elements=primary_keys,
            set_=update_dict,
            # index_where=index_where,
            where=index_where,
        )
    else:
        stmt = stmt.on_conflict_do_nothing(index_elements=primary_keys)

    foreign_keys = {col.name: list(col.foreign_keys)[0].column for col in table.columns if col.foreign_keys}
    unique_constraints = [c for c in table.constraints if isinstance(c, UniqueConstraint)]


    seen = set()
    conflict_keys = set()
    processed_rows = []
    for row in rows.to_dict(orient="records"):
        row = handle_foreignkeys_constraints(row, foreign_keys)
        row = validate_unique_constraints(row, unique_constraints, seen, conflict_keys)
        if row:
            processed_rows.append(row)

    if not processed_rows:
        return

    # Calculate max rows per batch based on PostgresSQL parameter limit
    num_columns = len(processed_rows[0]) if processed_rows else 0
    max_params = 65535
    max_rows_per_batch = max_params // num_columns if num_columns else 1

    if batch_size is None:
        batch_size = max_rows_per_batch
    elif batch_size > max_rows_per_batch:
        batch_size = max_rows_per_batch

    try:
        for batch in chunk_rows(processed_rows, batch_size):
            session.execute(stmt, batch)

    except Exception as e:
        if isinstance(my_logger, Logger):
            my_logger.error(f"Upsert failed with error: {e}.", exc_info=True)
            exc_type, exc_value, exc_tb = sys.exc_info()
            line_num = exc_tb.tb_lineno
            tb = traceback.TracebackException(exc_type, exc_value, exc_tb)
            my_logger.error(
                f"Line {line_num} Error encountered: {''.join(tb.format_exception_only())}",
                exc_info=True
            )
        raise e


def chunk_rows(rows, batch_size):
    iterator = iter(rows)
    while chunk := list(islice(iterator, batch_size)):
        yield chunk


def handle_foreignkeys_constraints(row, foreign_keys):
    for c_name, c_value in foreign_keys.items():
        foreign_obj = row.pop(c_value.table.name, None)
        if foreign_obj:
            row[c_name] = getattr(foreign_obj, c_value.name)
        else:
            row[c_name] = row.get(c_name)
    return row


def validate_unique_constraints(row, unique_constraints, seen, conflict_keys):
    """
    Validate and track unique constraints for a row.

    Parameters:
        row: Dictionary representing a row of data.
        unique_constraints: List of unique constraints for the table.
        seen: Set of already processed unique keys.
        conflict_keys: Set to track rows with unique constraint conflicts.

    Returns:
        Processed row or None if invalid.
    """
    for const in unique_constraints:
        unique_key = tuple((col.name, row[col.name]) for col in const.columns)
        if unique_key in seen:
            conflict_keys.add(unique_key)
        seen.add(unique_key)
    return row



async def upsert_async(
        session: AsyncSession,
        model: Type[Base],
        rows: pd.DataFrame,
        no_update_cols: tuple[str, ...] = (),
        on_conflict_update: bool = True,
        conflict_condition: list | BinaryExpression | BooleanClauseList | None = None,
        message_count: int = 0,
        batch_size: int | None = None,
        max_retries=5,
        retry_delay=1.0,
        my_logger: Logger|None=None,
) -> bool:
    """
    Perform an async upsert (insert or update on conflict) operation on the given SQLAlchemy model.

    Parameters:
        session: Async SQLAlchemy session.
        model: SQLAlchemy model class.
        rows: DataFrame containing rows to upsert.
        no_update_cols: Columns that should not be updated on conflict.
        on_conflict_update: Whether to perform updates on conflict.
        conflict_condition: Additional conditions for conflict resolution.
        batch_size: Maximum number of rows per batch.
        max_retries: Maximum number of retries for failed operations.
        retry_delay: Initial delay between retries (exponential backoff applied).
        my_logger: Logger instance for error logging.

    Returns:
        None
    """
    if rows.shape[0] == 0:
        return True

    rows = rows.copy()
    rows.replace({np.nan: None}, inplace=True)
    table = model.__table__
    stmt = insert(table)
    primary_keys = [key.name for key in inspect(table).primary_key]
    update_cols = [
        c.name for c in table.c
        if c not in list(table.primary_key.columns) and c.name not in no_update_cols
    ]
    update_dict = {k: getattr(stmt.excluded, k) for k in update_cols}
    # index_where = and_(*conflict_condition) if conflict_condition else None

    if conflict_condition is not None:
        if isinstance(conflict_condition, list):
            index_where = and_(
                *(getattr(model, col) < getattr(stmt.excluded, col) for col in conflict_condition)
            )
        else:
            index_where = conflict_condition
    else:
        index_where = None

    if on_conflict_update:
        stmt = stmt.on_conflict_do_update(
            index_elements=primary_keys,
            set_=update_dict,
            where=index_where,
        )
    else:
        stmt = stmt.on_conflict_do_nothing(index_elements=primary_keys)

    foreign_keys = {col.name: list(col.foreign_keys)[0].column for col in table.columns if col.foreign_keys}
    unique_constraints = [c for c in table.constraints if isinstance(c, UniqueConstraint)]

    seen = set()
    conflict_keys = set()
    processed_rows = []
    for row in rows.to_dict(orient="records"):
        row = handle_foreignkeys_constraints(row, foreign_keys)
        row = validate_unique_constraints(row, unique_constraints, seen, conflict_keys)
        if row:
            processed_rows.append(row)

    if not processed_rows:
        return True

    # Calculate max rows per batch based on PostgreSQL parameter limit
    num_columns = len(processed_rows[0]) if processed_rows else 0
    max_params = 65535
    max_rows_per_batch = max_params // num_columns if num_columns else 1

    if batch_size is None:
        batch_size = max_rows_per_batch
    elif batch_size > max_rows_per_batch:
        batch_size = max_rows_per_batch

    # last_error = None
    for batch in chunk_rows(processed_rows, batch_size):
        for attempt in range(max_retries):
            try:
                await asyncio.wait_for(session.execute(stmt, batch), timeout=30)
                break
            except DeadlockDetectedError as deadlock_err:
                if attempt < max_retries - 1:
                    wait_time = min(5, 1 * (2 ** attempt))
                    if isinstance(my_logger, Logger):
                        my_logger.warning(
                            f"Deadlock detected on attempt {attempt + 1}, retrying in {wait_time}s: {deadlock_err}")
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    if isinstance(my_logger, Logger):
                        my_logger.error(f"Deadlock persisted after {max_retries} attempts: {deadlock_err}")
                    return False

            except (IntegrityError, PendingRollbackError) as e:
                if isinstance(my_logger, Logger):
                    my_logger.error(f"Non-recoverable error in upsert_async: {type(e).__name__}: {e}")
                return False

            except asyncio.TimeoutError as timeout_err:
                if attempt < max_retries - 1:
                    wait_time = min(5, 1 * (2 ** attempt))
                    if isinstance(my_logger, Logger):
                        my_logger.warning(f"TimeoutError on attempt {attempt + 1}, retrying in {wait_time}s: {timeout_err}")
                        # Log actual SQL with values

                        explain_sql = compile_upsert_with_values(stmt, session.bind, batch)
                        my_logger.warning("EXPLAIN-ready SQL (with actual values):\n" + explain_sql)

                        # ✅ Run diagnostics
                        try:
                            stats = await get_pg_stat_activity(session)
                            my_logger.warning("Top slow queries during timeout:\n" +
                                              "\n".join(
                                                  f"{row['pid']}: {row['query']} (state={row['state']}, wait={row['wait_event']})"
                                                  for row in stats)
                                              )
                        except Exception as diag_err:
                            my_logger.error(f"Failed to collect pg_stat_activity: {diag_err}")

                        try:
                            blocking_info = await get_blocking_info(session)
                            my_logger.warning("Blocking info during timeout:\n" +
                                              "\n".join(
                                                  f"{row['blocking_pid']} is blocking {row['blocked_pid']}"
                                                  for row in blocking_info)
                                              )
                        except Exception as blocking_err:
                            my_logger.error(f"Failed to collect blocking info: {blocking_err}")


                    await asyncio.sleep(wait_time)
                    continue
            except Exception as e:
                exc_type, exc_value, exc_tb = sys.exc_info()
                line_num = exc_tb.tb_lineno
                tb = traceback.TracebackException(exc_type, exc_value, exc_tb)
                if isinstance(my_logger, Logger):
                    my_logger.error(f"Unexpected error during batch insert: {e}", exc_info=True)
                    my_logger.exception(
                        f"Line {line_num} Error encountered: {''.join(tb.format_exception_only())}",
                        exc_info=True
                    )
                return False
    return True



async def _save_error_batch(batch, model, error_type, my_logger):
    """Save problematic batch to file for analysis"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        df = pd.DataFrame(batch)
        filename = f"{error_type}_batch_{model.__table__.name}_{timestamp}.xlsx"
        await quick_save_async(df, filename, path=f"c:/vishal/log/batch_error")
        my_logger.debug(f"Saved error batch to {filename}")
    except Exception as save_err:
        my_logger.error(f"Failed to save error batch: {save_err}")

async def _rollback_savepoint_safely(savepoint, session, my_logger):
    """Safely rollback a savepoint, fallback to session rollback if needed"""
    rollback_successful = False
    if savepoint:
        try:
            await savepoint.rollback()
            my_logger.debug("Successfully rolled back savepoint")
            rollback_successful = True
        except Exception as rollback_err:
            my_logger.error(f"Failed to rollback savepoint: {rollback_err}")

        try:
            # Force rollback of any remaining transaction state
            if session.in_transaction():
                await session.rollback()
                my_logger.warning("Rolled back entire session as fallback")
            await session.begin()
            my_logger.debug("Started fresh transaction")
            # Test if session is now healthy
            await session.execute(text("SELECT 1"))
            my_logger.debug("Session state verified as healthy")

        except Exception as session_err:
            my_logger.error(f"Session still unhealthy after rollbacks: {session_err}")
            # Last resort: close and invalidate the connection
            try:
                await session.close()
                my_logger.warning("Closed session due to persistent invalid state")
                raise RuntimeError("Session closed due to invalid savepoint state - needs recreation")
            except Exception as close_err:
                my_logger.error(f"Failed to close session: {close_err}")
                raise RuntimeError("Cannot recover session state") from session_err
